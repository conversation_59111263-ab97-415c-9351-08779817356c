import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { DatePipe } from '@angular/common';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LayoutComponent } from './components/layout/layout.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { ProfileComponent } from './pages/profile/profile.component';
import { OtpVerifyComponent } from './pages/otp-verify/otp-verify.component';
import { ThemeToggleComponent } from './components/theme-toggle/theme-toggle.component';
import { AnonymousLayoutComponent } from './components/anonymous-layout/anonymous-layout.component';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { AuthComponent } from './pages/auth/auth.component';
import { CodeInputModule } from 'angular-code-input';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { AuthInterceptor } from './web/auth.interceptor';
import { ResponseInterceptor } from './web/response.interceptor';

import { register } from 'swiper/element/bundle';
import { GoldCardComponent } from './components/cards/gold-card/gold-card.component';
import { DebitCardComponent } from './components/cards/debit-card/debit-card.component';
import { OrderFormComponent } from './components/order-form/order-form.component';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FormsModule } from '@angular/forms';
import { HistoryComponent } from './pages/history/history.component';
import { SupportComponent } from './pages/support/support.component';
import { AboutComponent } from './pages/about/about.component';
import { PersonIdentificationComponent } from './components/person-identification/person-identification.component';
import { ProfileFormComponent } from './components/profile-form/profile-form.component';
import { HistoryTabbarComponent } from './components/history-tabbar/history-tabbar.component';
import { OrderHistoryRowComponent } from './components/order-history-row/order-history-row.component';
import { PaymentHistoryRowComponent } from './components/payment-history-row/payment-history-row.component';
import { SupportTabbarComponent } from './components/support-tabbar/support-tabbar.component';
import { TicketFormComponent } from './components/ticket-form/ticket-form.component';
import { TicketRowComponent } from './components/ticket-row/ticket-row.component';

register()

@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent,
    DashboardComponent,
    ProfileComponent,
    OtpVerifyComponent,
    AnonymousLayoutComponent,
    ThemeToggleComponent,
    AuthComponent,
    GoldCardComponent,
    DebitCardComponent,
    OrderFormComponent,
    HistoryComponent,
    SupportComponent,
    AboutComponent,
    PersonIdentificationComponent,
    ProfileFormComponent,
    HistoryTabbarComponent,
    OrderHistoryRowComponent,
    PaymentHistoryRowComponent,
    SupportTabbarComponent,
    TicketFormComponent,
    TicketRowComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    NgxMaskDirective,
    NgxMaskPipe,
    CodeInputModule,
    NgApexchartsModule,
    FormsModule
  ],
  providers: [
    DatePipe,
    provideNgxMask(),
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ResponseInterceptor,
      multi: true
    },
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppModule { }
