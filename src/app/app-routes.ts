import { Routes } from "@angular/router";
import { LayoutComponent } from "./components/layout/layout.component";
import { DashboardComponent } from "./pages/dashboard/dashboard.component";
import { OtpVerifyComponent } from "./pages/otp-verify/otp-verify.component";
import { ProfileComponent } from "./pages/profile/profile.component";
import { AnonymousLayoutComponent } from "./components/anonymous-layout/anonymous-layout.component";
import { AuthComponent } from "./pages/auth/auth.component";
import { HistoryComponent } from "./pages/history/history.component";
import { AboutComponent } from "./pages/about/about.component";
import { SupportComponent } from "./pages/support/support.component";

export const ROUTES: Routes = [
    {
        path: '',
        component: LayoutComponent,
        // canActivate: [authGuard],
        children: [
            { path: '', redirectTo: `/dashboard`, pathMatch: 'full' },
            { path: 'dashboard', component: DashboardComponent },
            { path: 'profile', component: ProfileComponent },
            { path: 'history', component: HistoryComponent },
            { path: 'support', component: SupportComponent },
            { path: 'about', component: AboutComponent },
        ],
    },
    {
        path: '',
        component: AnonymousLayoutComponent,
        // canActivate: [noAuthGuard],
        children: [
            { path: '', redirectTo: `/auth`, pathMatch: 'full' },
            { path: 'auth', component: AuthComponent },
            { path: 'verify/:contact_value', component: OtpVerifyComponent },
        ],
    },
    { path: '**', redirectTo: 'auth' },
];