import { Component } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { LoadingService } from './services/loading.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  constructor(private loadingService: LoadingService) {

    this.loadingService
      .onChange()
      .pipe(untilDestroyed(this))
      .subscribe({
        next: (val: boolean) => {
          this.loading.next(val);
        },
        complete: () => {
          this.loading.next(false);
        },
      });
  }

  public loading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

}
