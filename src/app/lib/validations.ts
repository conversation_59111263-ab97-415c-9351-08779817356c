import { PersianDateRegex } from "../constants";


export function IsValidPersianDate(date: string): boolean {
    const value: string = date.replaceAll(' ', '');

    if (!PersianDateRegex.test(value)) {
        return false;
    }

    const split: string[] = value.split('/');
    if (split.length !== 3) {
        return false;
    }


    const year: number = parseInt(split[0]);
    const month: number = parseInt(split[1]);
    const day: number = parseInt(split[2]);

    if (month > 6 && day > 30) {   
        return false;
    }

    return true;
}