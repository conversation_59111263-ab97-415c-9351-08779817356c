export interface UserDTO {
    // user unique id
    id: number;
    // unique username for future use
    username: string;
    // user primary mobile phone number for authorization use
    mobile: string;
    // user primary e-mail address for future use
    email: string;
    // user assigned roles for permission controls
    roles: Array<string>;
    // sets to true if user verified his mobile
    is_mobile_verified: boolean;
    // sets to true if user verified his e-mail address
    is_email_verified: boolean;
    // is user approved or no
    approved: boolean;
    // is user banned or no
    banned: boolean;
    // for future use
    meta_tag: string;
    // for future use
    meta_data: string;
    // expire time of user, if not sets then user valid for unlimited time
    expires_at: Date;
    // when user was created
    created_at: Date;
    // when user was updated
    updated_at?: Date;
}