import { UserDTO } from "./user.dto"

export interface VerificationOutput {
    // user entity model
    user: UserDTO | null;
    // access token that generated for authentication
    access_token: string;
    // token for refreshing access token
    refresh_token: string;
    // token expiration time
    access_token_expires_at: string;
    // token expiration time
    refresh_token_expires_at: string;
    // identifier of mqtt client endpoint
    client_id: string;
}

export const InitialVerificationOutput: VerificationOutput = {
    user: null,
    access_token: "",
    refresh_token: "",
    access_token_expires_at: "",
    refresh_token_expires_at: "",
    client_id: ""
}