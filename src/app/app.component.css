.loading-overlay {
    @apply fixed inset-0 z-[9999] flex items-center justify-center;
    @apply bg-slate-500/50 backdrop-blur;
}

.loader {
    @apply ease-linear rounded-full border-t-4 border-4;
    @apply border-slate-50 dark:border-slate-700 border-t-amber-500 dark:border-t-amber-400;
    -webkit-animation: spinner 1.5s linear infinite;
    animation: spinner 1.5s linear infinite;
}

.loader.light {
    @apply ease-linear rounded-full border-t-4 border-4;
    @apply border-white/50 border-t-white;
    -webkit-animation: spinner 1.5s linear infinite;
    animation: spinner 1.5s linear infinite;
}

@-webkit-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}