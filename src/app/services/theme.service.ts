import { APP_ID, Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';

const THEME_KEY = 'THEME';
export type ThemeValue = 'light' | 'dark';

@UntilDestroy()
@Injectable({
  providedIn: 'root'
})
export class ThemeService {

  theme$?: BehaviorSubject<string>;

  constructor(@Inject(PLATFORM_ID) private platformId: object, @Inject(APP_ID) private appId: string) {
    let storedValue: ThemeValue = 'light'

    if (!localStorage.getItem(THEME_KEY)) {
      localStorage.setItem(THEME_KEY, 'dark');
      storedValue = 'dark';
    } else {
      storedValue = localStorage.getItem(THEME_KEY) as ThemeValue;
    }

    this.theme$ = new BehaviorSubject<string>(storedValue);

    this.theme$.pipe(untilDestroyed(this)).subscribe((value) => {
      if (['dark', 'light'].includes(value)) {
        const htmlTag = document.getElementsByTagName('html').item(0);
        const classList = htmlTag?.classList;

        localStorage.setItem(THEME_KEY, value);

        if (value === 'dark') {
          classList?.add(value);
        } else {
          classList?.remove('dark');
        }
      }
    });
  }

  public toggleTheme(): void {
    const currentValue = this.theme$?.value;
    this.theme$?.next(currentValue === 'dark' ? 'light' : 'dark');
  }
}