import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import NotificationDataDTO, { InitialNotificationDataDTO } from '../models/notification_data.dto';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  private notificationDataSubject = new BehaviorSubject<NotificationDataDTO>(InitialNotificationDataDTO);

  public notificationData$ = this.notificationDataSubject.asObservable();

  constructor() { }

  setMarketData(marketData: NotificationDataDTO) {
    this.notificationDataSubject.next(marketData);
  }
  
}
