import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import MarketDataDTO, { InitialMarketDataDTO } from '../models/market_data.dto';

@Injectable({
  providedIn: 'root'
})
export class MarketService {

  private marketDataSubject = new BehaviorSubject<MarketDataDTO>(InitialMarketDataDTO);

  public marketData$ = this.marketDataSubject.asObservable();

  constructor() { }

  setMarketData(marketData: MarketDataDTO) {
    this.marketDataSubject.next(marketData);
  }
  
}
