import { Injectable } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Observable, Subject } from 'rxjs';

@UntilDestroy()
@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private subject = new Subject<boolean>();

  constructor() { }

  onChange(): Observable<boolean> {
    return this.subject.asObservable().pipe(untilDestroyed(this));
  }

  next(val: boolean) {
    this.subject.next(val);
  }
}