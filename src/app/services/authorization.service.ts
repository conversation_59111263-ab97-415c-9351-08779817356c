import { Injectable } from '@angular/core';
import { AuthorizationKey } from '../constants';
import { Observable } from 'rxjs';
import { InitialVerificationOutput, VerificationOutput } from '../models/verification.output';
import { ResponseDTO } from '../models/response.dto';
import { environment } from '../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class AuthorizationService {
//   constructor(private service: AuthenticationService) { }

  public isAuthenticated(): boolean {
    // const data: string | null = localStorage.getItem(AuthorizationKey);

    // if (!data) {
    //   localStorage.setItem(AuthorizationKey, JSON.stringify(InitialVerificationOutput));
    //   return false;
    // }

    // const auth: VerificationOutput = JSON.parse(data) as VerificationOutput;

    // const currentDate = new Date();
    // const expire = auth.access_token_expires_at.length ? Date.parse(auth.refresh_token_expires_at) : new Date().getTime();

    // if (!auth.access_token.length || expire <= currentDate.getTime()) {

    //   return false;
    // }

    return true;
  }

  public setToken(data: VerificationOutput): void {
    localStorage.setItem(AuthorizationKey, JSON.stringify(data));
  }

  public getToken(): string | undefined {
    // const data: string | null = localStorage.getItem(AuthorizationKey);

    // if (!data) {
    //   return undefined;
    // }

    // const auth: VerificationOutput = JSON.parse(data) as VerificationOutput;

    // const currentDate = new Date();
    // const expire = auth.access_token_expires_at.length ? Date.parse(auth.refresh_token_expires_at) : new Date().getTime();

    // if (!auth.access_token.length || expire <= currentDate.getTime()) {
    //   return undefined;
    // }

    // return auth.access_token;
    return "";
  }

  public refreshToken(): Observable<ResponseDTO<VerificationOutput>> | undefined {
    // const data: string | null = localStorage.getItem(AuthorizationKey);

    // if (!data) {
    //   return undefined;
    // }

    // const auth: VerificationOutput = JSON.parse(data) as VerificationOutput;

    // const currentDate = new Date();
    // const expire = auth.access_token_expires_at.length ? Date.parse(auth.refresh_token_expires_at) : new Date().getTime();

    // if (auth.access_token.length && auth.refresh_token.length && expire <= currentDate.getTime() && environment.IsProduction) {
    // //   return this.service.RefreshToken(auth.refresh)
    // }

    return undefined
  }
}