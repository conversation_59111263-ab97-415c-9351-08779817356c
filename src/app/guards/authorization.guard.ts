import { inject } from '@angular/core';
import { AuthorizationService } from '../services/authorization.service';
import { Router } from '@angular/router';

export const authGuard = () => {
  const authService = inject(AuthorizationService);
  const router = inject(Router);

  if (authService.isAuthenticated()) {
    return true;
  }

  return router.parseUrl('/auth');
};

export const noAuthGuard = () => {
  const authService = inject(AuthorizationService);
  const router = inject(Router);

  if (!authService.isAuthenticated()) {
    return true;
  }

  return router.parseUrl('/dashboard');
};