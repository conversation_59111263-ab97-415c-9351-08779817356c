import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import BaseResponseDTO from '../models/response.dto';

@Injectable()
export class ResponseInterceptor implements HttpInterceptor {

    intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        return next.handle(req).pipe(map((event: HttpEvent<any>) => {
            if (event instanceof HttpResponse) {
                const response = this.parseBody(event.body);
                if (response.errors) {
                    throw new Error(response.errors[0]);
                }
                event = event.clone({ body: this.parseBody(event.body) });
            }
            return event;
        }));
    }

    private parseBody(body: any): BaseResponseDTO {
        const json = JSON.parse(JSON.stringify(body));
        return json as BaseResponseDTO;
    }
}
