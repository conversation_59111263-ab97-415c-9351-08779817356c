import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SignInInput } from '../models/sign_in.input';
import { ResponseDTO } from '../models/response.dto';
import { environment } from '../../environments/environment.development';
import { SignUpInput } from '../models/sign_up.input';
import { VerificationInput } from '../models/verification.input';
import { VerificationOutput } from '../models/verification.output';
import { RefreshTokenInput } from '../models/refresh_token.input';
import { UserDTO } from '../models/user.dto';


@Injectable({
    providedIn: 'root'
})
export class UserApi {

    constructor(private http: HttpClient) { }

    public ContextUser(): Observable<ResponseDTO<UserDTO>> {
        return this.http.get<ResponseDTO<UserDTO>>(`${environment.ApiBaseURL}/user/sign_in`);
    }
}
