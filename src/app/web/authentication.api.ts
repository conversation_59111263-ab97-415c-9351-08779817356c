import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SignInInput } from '../models/sign_in.input';
import { ResponseDTO } from '../models/response.dto';
import { environment } from '../../environments/environment.development';
import { SignUpInput } from '../models/sign_up.input';
import { VerificationInput } from '../models/verification.input';
import { VerificationOutput } from '../models/verification.output';
import { RefreshTokenInput } from '../models/refresh_token.input';


@Injectable({
    providedIn: 'root'
})
export class AuthenticationApi {

    constructor(private http: HttpClient) { }

    public SignIn(input: SignInInput): Observable<ResponseDTO<string>> {
        return this.http.post<ResponseDTO<string>>(
            `${environment.ApiBaseURL}/user/sign_in`,
            JSON.stringify(input)
        );
    }

    public SignUp(input: SignUpInput): Observable<ResponseDTO<string>> {
        return this.http.post<ResponseDTO<string>>(
            `${environment.ApiBaseURL}/user/sign_up`,
            JSON.stringify(input)
        );
    }

    public OtpVerify(input: VerificationInput): Observable<ResponseDTO<VerificationOutput>> {
        return this.http.post<ResponseDTO<VerificationOutput>>(
            `${environment.ApiBaseURL}/user/otp/verify`,
            JSON.stringify(input)
        );
    }

    public RefreshToken(input: RefreshTokenInput): Observable<ResponseDTO<VerificationOutput>> {
        return this.http.post<ResponseDTO<VerificationOutput>>(
            `${environment.ApiBaseURL}/user/token/refresh`,
            JSON.stringify({ token: input })
        );
    }
}
