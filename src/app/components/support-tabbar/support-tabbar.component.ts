import { Component, EventEmitter, Input, Output } from '@angular/core';

export type SelectedSupportTab = 'CREATE_TICKET' | 'MY_TICKETS';

@Component({
  selector: 'app-support-tabbar',
  templateUrl: './support-tabbar.component.html',
  styleUrl: './support-tabbar.component.css'
})
export class SupportTabbarComponent {
  @Input() defaultTab: SelectedSupportTab = 'CREATE_TICKET';
  @Output() onTabChange = new EventEmitter<SelectedSupportTab>();

  public selectedTab: SelectedSupportTab;
  
  constructor() {
    this.selectedTab = this.defaultTab;
  }

  public setSelectedTab(tab: SelectedSupportTab): void {
    this.onTabChange.emit(tab);
    this.selectedTab = tab;
  }
}
