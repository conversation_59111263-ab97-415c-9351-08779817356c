<div class="tabbar">
    <ul>
        <li>
            <button [ngClass]="selectedTab === 'CREATE_TICKET' ? 'active' : ''" (click)="setSelectedTab('CREATE_TICKET')">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                <h6>ایجاد تیکت</h6>
            </button>
        </li>

        <li>
            <button [ngClass]="selectedTab === 'MY_TICKETS' ? 'active' : ''" (click)="setSelectedTab('MY_TICKETS')">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                </svg>
                <h6>تیکت های من</h6>
            </button>
        </li>
    </ul>
</div>
