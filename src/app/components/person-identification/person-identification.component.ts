import { Component } from '@angular/core';
import { PersianDateRegex } from '../../constants';
import { IsValidPersianDate } from '../../lib/validations';

@Component({
  selector: 'app-person-identification',
  templateUrl: './person-identification.component.html',
  styleUrl: './person-identification.component.css'
})
export class PersonIdentificationComponent {


  public birthDate: string = '';
  public birthDateError?: string = undefined;

  public onBirthDateChange(event: Event): void {
    const target = event.target as HTMLInputElement;

    if (IsValidPersianDate(target.value)) {
      this.birthDate = target.value;
      this.birthDateError = undefined;
    } else {
      this.birthDate = '';
      this.birthDateError = 'تاریخ تولد نامعتبر است';
    }
  }
}
