import { Component, OnInit } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';
import { MarketService } from '../../services/market.service';
import MarketDataDTO, { InitialMarketDataDTO } from '../../models/market_data.dto';

@UntilDestroy()
@Component({
  selector: 'app-order-form',
  templateUrl: './order-form.component.html',
  styleUrl: './order-form.component.css'
})
export class OrderFormComponent {
  constructor(private marketService: MarketService) {
    this.marketService.marketData$.pipe(untilDestroyed(this)).subscribe((marketData) => {
      this.cash = marketData.buy * this.weight;
    })
  }

  public weight: number = 0.000;
  public cash: number = 0;

  private marketDataSubject = new BehaviorSubject<MarketDataDTO>(InitialMarketDataDTO);
  public marketData$ = this.marketDataSubject.asObservable();

  public cashAmountChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value: string = target.value.trim().replaceAll(',', '').replaceAll(' ', '');
    const valueNum = parseInt(value);

    if (valueNum) {
      this.cash = valueNum;

      if (this.cash > 0) {
        const newWeight = this.cash / this.marketDataSubject.getValue().buy;
        console.log("fixed 3 weight: ", newWeight);
        this.weight = newWeight;
      }
    }
  }
}
