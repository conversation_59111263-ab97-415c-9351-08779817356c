import { Component, OnInit } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';
import { MarketService } from '../../services/market.service';
import MarketDataDTO, { InitialMarketDataDTO } from '../../models/market_data.dto';

@UntilDestroy()
@Component({
  selector: 'app-order-form',
  templateUrl: './order-form.component.html',
  styleUrl: './order-form.component.css'
})
export class OrderFormComponent {

  public credit: number = 0.000;
  public cash: number = 0;
  public discount: number = 3.0;

  public cashAmountChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value: string = target.value.trim().replaceAll(',', '').replaceAll(' ', '');
    const valueNum = parseInt(value);

    if (valueNum) {
      this.cash = valueNum;

      if (this.cash > 0) {
        // calculate credit to be received with discount
        const newCredit  = this.cash + (this.cash * this.discount) / 100;
        console.log("fixed 3 weight: ", newCredit);
        this.credit = newCredit;
      }
    }
  }
}
