.card {
    @apply p-4 rounded-xl shadow-md my-3;
    @apply shadow-slate-200 dark:shadow-slate-950;
    @apply flex flex-col;
    @apply mx-3 ring-1 ring-inset aspect-[9/5];
}

.card.gold {
    @apply ring-yellow-500 dark:ring-yellow-600;
    @apply bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))];
    @apply from-amber-500 via-amber-400  to-amber-600;
}

.card .header {
    @apply flex items-center justify-between;
}

.card .header h1 {
    @apply font-bold text-right flex-grow;
}

.card svg {
    @apply size-7 text-slate-500;
}

.card.gold .header h1 {
    @apply text-slate-800 drop-shadow;
}

.card .body {
    @apply mt-6 flex-grow;
}

.card .body h6 {
    @apply text-right text-amber-800 font-bold;
}

.card .body .balance {
    @apply flex flex-row-reverse space-x-reverse space-x-2 items-baseline justify-start;
}

.card .body .balance .weight {
    @apply font-bold text-3xl font-sans text-white drop-shadow-[0_1.2px_1.2px_rgba(113,62,18,1.0)];
}

.card .body .balance .weight-separator {
    @apply text-slate-700 text-sm font-bold;
}

.card .balance-value {
    @apply flex flex-row-reverse space-x-reverse space-x-2 items-baseline justify-between;
}

.card .balance-value .title {
    @apply text-sm font-bold text-slate-700 flex-grow text-right;
}

.card .balance-value .value {
    @apply font-bold text-lg font-sans text-amber-900;
}

.card .balance-value .currency {
    @apply font-medium text-sm text-slate-700;
}
