.card {
    @apply p-4 rounded-xl shadow-md my-3;
    @apply shadow-slate-200 dark:shadow-slate-950;
    @apply flex flex-col;
    @apply mx-3 ring-1 ring-inset aspect-[9/5];
}

.card.debit {
    @apply ring-slate-300 dark:ring-slate-700;
    @apply bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))];
    @apply from-slate-500 via-slate-300  to-slate-400;
}

.card .header {
    @apply flex items-center justify-between;
}

.card .header h1 {
    @apply font-bold text-right flex-grow;
    @apply text-slate-800 drop-shadow;
}

.card svg {
    @apply size-7 text-slate-300;
}

.card .body {
    @apply mt-4 flex-grow;
}

.card .body h6 {
    @apply text-right text-sky-900 font-bold text-sm;
}

.card .body .balance {
    @apply flex flex-row-reverse space-x-reverse space-x-2 items-baseline justify-start;
}

.card .body .balance .value {
    @apply font-bold text-xl font-sans text-sky-800 drop-shadow;
}

.card .body .balance .currency {
    @apply text-slate-700 text-sm font-bold;
}

.card .card-info {
    @apply grid grid-cols-2 gap-1 py-3 w-full mx-auto;
}

.card .card-info .number {
    @apply font-mono text-2xl font-medium text-slate-700 flex-grow tracking-widest;
    @apply col-span-2;
}

.card .card-info .cvv,
.card .card-info .expire {
    @apply font-bold text-sm text-slate-700 font-mono;
}

.card .card-info .cvv {
    @apply text-left;
}

.card .card-info .expire {
    @apply text-right pr-1;
}
