@if (isOpen) {
<div class="modal-backdrop" (click)="onBackdropClick($event)">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">{{ title }}</h3>
            <button class="modal-close-btn" (click)="closeModal()">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" 
                    stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <div class="modal-body">
            <div class="options-list">
                @for (option of options; track option.value) {
                    <button class="option-item" 
                        [class.selected]="option.value === selectedValue"
                        (click)="selectOption(option.value)">
                        
                        @if (option.icon) {
                            <div class="option-icon" [innerHTML]="option.icon"></div>
                        }
                        
                        <div class="option-content">
                            <span class="option-label" [class]="option.color || ''">{{ option.label }}</span>
                            @if (option.value === selectedValue) {
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" 
                                    class="size-8 text-amber-500">
                                    <path fill-rule="evenodd" 
                                        d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" 
                                        clip-rule="evenodd" />
                                </svg>
                            }
                        </div>
                    </button>
                }
            </div>
        </div>
    </div>
</div>
}
