.modal-backdrop {
    @apply fixed inset-0 z-50 flex items-center justify-center;
    @apply bg-slate-900/50 backdrop-blur-sm;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-container {
    @apply bg-white dark:bg-slate-800 rounded-xl shadow-xl;
    @apply ring-1 ring-slate-200 dark:ring-slate-700;
    @apply w-full max-w-sm mx-4;
    animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    @apply flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700;
}

.modal-title {
    @apply text-lg font-bold text-slate-800 dark:text-slate-100 text-right flex-grow;
}

.modal-close-btn {
    @apply p-1 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors;
    @apply text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200;
}

.modal-body {
    @apply p-2;
}

.options-list {
    @apply space-y-2;
}

.option-item {
    @apply w-full flex flex-row-reverse space-x-reverse items-center space-x-3 justify-between p-3 rounded-lg;
    @apply bg-slate-50 hover:bg-slate-100 dark:bg-slate-700 dark:hover:bg-slate-600;
    @apply border border-transparent hover:border-slate-200 dark:hover:border-slate-600;
    @apply transition-all duration-150;
}

.option-item.selected {
    @apply bg-amber-50 hover:bg-amber-100 dark:bg-amber-700/20 dark:hover:bg-amber-700/30;
}

.option-icon {
    @apply flex-shrink-0;
}

.option-content {
    @apply flex flex-row-reverse space-x-reverse space-x-3 items-center justify-between flex-grow;
}

.option-label {
    @apply font-medium text-slate-700 dark:text-slate-200;
    @apply text-right flex-grow;
}

.option-item.selected .option-label {
    @apply text-amber-700 dark:text-amber-300;
}

/* Additional polish */
.modal-container {
    @apply max-h-[80vh] overflow-hidden;
}

.modal-body {
    @apply max-h-[60vh] overflow-y-auto;
}

.option-item:active {
    @apply scale-[0.98];
}

/* Custom scrollbar for modal body */
.modal-body::-webkit-scrollbar {
    @apply w-2;
}

.modal-body::-webkit-scrollbar-track {
    @apply bg-slate-100 dark:bg-slate-700 rounded-full;
}

.modal-body::-webkit-scrollbar-thumb {
    @apply bg-slate-300 dark:bg-slate-600 rounded-full;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-400 dark:bg-slate-500;
}
