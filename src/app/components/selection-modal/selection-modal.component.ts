import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface SelectionOption {
  value: string;
  label: string;
  color?: string;
  icon?: string;
}

@Component({
  selector: 'app-selection-modal',
  templateUrl: './selection-modal.component.html',
  styleUrl: './selection-modal.component.css'
})
export class SelectionModalComponent {
  @Input() isOpen: boolean = false;
  @Input() title: string = '';
  @Input() options: SelectionOption[] = [];
  @Input() selectedValue: string = '';
  @Output() onSelect = new EventEmitter<string>();
  @Output() onClose = new EventEmitter<void>();

  public selectOption(value: string): void {
    this.onSelect.emit(value);
    this.closeModal();
  }

  public closeModal(): void {
    this.onClose.emit();
  }

  public onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }

  public getSelectedOption(): SelectionOption | undefined {
    return this.options.find(option => option.value === this.selectedValue);
  }
}
