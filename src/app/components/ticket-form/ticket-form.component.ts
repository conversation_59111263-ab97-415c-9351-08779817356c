import { Component, EventEmitter, Output } from '@angular/core';
import { CreateTicketRequest, TicketCategory, TicketPriority } from '../../models/support-ticket.model';

@Component({
  selector: 'app-ticket-form',
  templateUrl: './ticket-form.component.html',
  styleUrl: './ticket-form.component.css'
})
export class TicketFormComponent {
  @Output() onSubmit = new EventEmitter<CreateTicketRequest>();

  public title: string = '';
  public description: string = '';
  public category: TicketCategory = 'GENERAL';
  public priority: TicketPriority = 'MEDIUM';

  public categories = [
    { value: 'TECHNICAL', label: 'مسائل فنی' },
    { value: 'PAYMENT', label: 'پرداخت و تراکنش' },
    { value: 'ACCOUNT', label: 'حساب کاربری' },
    { value: 'GENERAL', label: 'عمومی' }
  ];

  public priorities = [
    { value: 'LOW', label: 'کم', color: 'text-slate-600' },
    { value: 'MEDIUM', label: 'متوسط', color: 'text-amber-600' },
    { value: 'HIGH', label: 'بالا', color: 'text-orange-600' },
    { value: 'URGENT', label: 'فوری', color: 'text-red-600' }
  ];

  public submitTicket(): void {
    if (this.isFormValid()) {
      const ticket: CreateTicketRequest = {
        title: this.title,
        description: this.description,
        category: this.category,
        priority: this.priority
      };
      this.onSubmit.emit(ticket);
      this.resetForm();
    }
  }

  private isFormValid(): boolean {
    return this.title.trim().length > 0 && this.description.trim().length > 0;
  }

  private resetForm(): void {
    this.title = '';
    this.description = '';
    this.category = 'GENERAL';
    this.priority = 'MEDIUM';
  }
}
