import { Component, EventEmitter, Output } from '@angular/core';
import { CreateTicketRequest, TicketCategory, TicketPriority } from '../../models/support-ticket.model';
import { SelectionOption } from '../selection-modal/selection-modal.component';

@Component({
  selector: 'app-ticket-form',
  templateUrl: './ticket-form.component.html',
  styleUrl: './ticket-form.component.css'
})
export class TicketFormComponent {
  @Output() onSubmit = new EventEmitter<CreateTicketRequest>();

  public title: string = '';
  public description: string = '';
  public category: TicketCategory = 'GENERAL';
  public priority: TicketPriority = 'MEDIUM';

  // Modal states
  public isCategoryModalOpen: boolean = false;
  public isPriorityModalOpen: boolean = false;

  public categories: SelectionOption[] = [
    {
      value: 'TECHNICAL',
      label: 'مسائل فنی',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-blue-500"><path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" /></svg>'
    },
    {
      value: 'PAYMENT',
      label: 'پرداخت و تراکنش',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-green-500"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" /></svg>'
    },
    {
      value: 'ACCOUNT',
      label: 'حساب کاربری',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-purple-500"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" /></svg>'
    },
    {
      value: 'GENERAL',
      label: 'عمومی',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-slate-500"><path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg>'
    }
  ];

  public priorities: SelectionOption[] = [
    { value: 'LOW', label: 'کم', color: 'text-slate-600' },
    { value: 'MEDIUM', label: 'متوسط', color: 'text-amber-600' },
    { value: 'HIGH', label: 'بالا', color: 'text-orange-600' },
    { value: 'URGENT', label: 'فوری', color: 'text-red-600' }
  ];

  public submitTicket(): void {
    if (this.isFormValid()) {
      const ticket: CreateTicketRequest = {
        title: this.title,
        description: this.description,
        category: this.category,
        priority: this.priority
      };
      this.onSubmit.emit(ticket);
      this.resetForm();
    }
  }

  private isFormValid(): boolean {
    return this.title.trim().length > 0 && this.description.trim().length > 0;
  }

  private resetForm(): void {
    this.title = '';
    this.description = '';
    this.category = 'GENERAL';
    this.priority = 'MEDIUM';
  }

  // Modal methods
  public openCategoryModal(): void {
    this.isCategoryModalOpen = true;
  }

  public closeCategoryModal(): void {
    this.isCategoryModalOpen = false;
  }

  public onCategorySelect(value: string): void {
    this.category = value as TicketCategory;
    this.closeCategoryModal();
  }

  public openPriorityModal(): void {
    this.isPriorityModalOpen = true;
  }

  public closePriorityModal(): void {
    this.isPriorityModalOpen = false;
  }

  public onPrioritySelect(value: string): void {
    this.priority = value as TicketPriority;
    this.closePriorityModal();
  }

  public getSelectedCategoryLabel(): string {
    const selected = this.categories.find(cat => cat.value === this.category);
    return selected ? selected.label : '';
  }

  public getSelectedPriorityLabel(): string {
    const selected = this.priorities.find(prio => prio.value === this.priority);
    return selected ? selected.label : '';
  }

  public getSelectedPriorityColor(): string {
    const selected = this.priorities.find(prio => prio.value === this.priority);
    return selected ? selected.color || '' : '';
  }
}
