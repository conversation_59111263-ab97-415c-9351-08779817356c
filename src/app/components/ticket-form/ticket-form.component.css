.card-row .title {
    @apply font-bold text-slate-700 dark:text-slate-200;
    @apply text-right text-base;
}

.button:disabled {
    @apply opacity-50 cursor-not-allowed;
    @apply hover:bg-indigo-500;
}

select.input-text {
    @apply cursor-pointer;
}

textarea.input-text {
    @apply resize-none;
    @apply min-h-[100px];
}

.selection-button {
    @apply w-full flex flex-row-reverse items-center justify-between px-3 py-2;
    @apply bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600;
    @apply rounded-lg text-right;
    @apply hover:border-slate-400 dark:hover:border-slate-500;
    @apply focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent;
    @apply transition-all duration-150;
}

.selection-button:hover {
    @apply bg-slate-50 dark:bg-slate-700;
}

.selection-text {
    @apply text-slate-700 dark:text-slate-200 font-medium;
    @apply flex-grow text-right;
}
