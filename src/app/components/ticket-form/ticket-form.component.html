<div class="card-row">
    <div class="flex flex-row-reverse space-x-reverse space-x-4 items-center justify-between">
        <h6 class="title">ایجاد تیکت پشتیبانی جدید</h6>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" 
            stroke="currentColor" class="size-6 text-amber-500">
            <path stroke-linecap="round" stroke-linejoin="round" 
                d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
        </svg>
    </div>

    <div class="input-group">
        <label class="label-text rtl">عنوان تیکت</label>
        <input type="text" class="input-text rtl text-right" [(ngModel)]="title" 
            placeholder="عنوان مشکل یا سوال خود را وارد کنید" />
    </div>

    <div class="input-group">
        <label class="label-text rtl">دسته‌بندی</label>
        <button type="button" class="selection-button" (click)="openCategoryModal()">
            <span class="selection-text">{{ getSelectedCategoryLabel() }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-5 text-slate-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
            </svg>
        </button>
    </div>

    <div class="input-group">
        <label class="label-text rtl">اولویت</label>
        <button type="button" class="selection-button" (click)="openPriorityModal()">
            <span class="selection-text" [class]="getSelectedPriorityColor()">{{ getSelectedPriorityLabel() }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-5 text-slate-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
            </svg>
        </button>
    </div>

    <div class="input-group">
        <label class="label-text rtl">توضیحات</label>
        <textarea class="input-text" rows="4" [(ngModel)]="description" 
            placeholder="توضیح کاملی از مشکل یا سوال خود ارائه دهید"></textarea>
    </div>

    <button class="button indigo iconic mt-6 mb-2" (click)="submitTicket()" 
        [disabled]="!title.trim() || !description.trim()">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" 
            stroke="currentColor" class="size-7">
            <path stroke-linecap="round" stroke-linejoin="round" 
                d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5" />
        </svg>
        <div class="font-bold">ارسال تیکت</div>
    </button>
</div>

<!-- Category Selection Modal -->
<app-selection-modal
    [isOpen]="isCategoryModalOpen"
    [title]="'انتخاب دسته‌بندی'"
    [options]="categories"
    [selectedValue]="category"
(onSelect)="onCategorySelect($event)"
    (onClose)="closeCategoryModal()">
</app-selection-modal>

<!-- Priority Selection Modal -->
<app-selection-modal
    [isOpen]="isPriorityModalOpen"
    [title]="'انتخاب اولویت'"
    [options]="priorities"
    [selectedValue]="priority"
(onSelect)="onPrioritySelect($event)"
    (onClose)="closePriorityModal()">
</app-selection-modal>
