import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { filter } from 'rxjs';

@UntilDestroy()
@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.css'
})
export class LayoutComponent {
  public currentPage: string = 'dashboard';

  constructor(private router: Router) {
    this.router.events.pipe(
      filter((e) => e instanceof NavigationEnd)
    ).subscribe((e) => {
      const event = e as NavigationEnd;
      if (event) {
        this.currentPage = event.urlAfterRedirects.replaceAll('/', '');
      }
    })
  }
}
