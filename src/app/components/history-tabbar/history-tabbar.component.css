.tabbar {
    @apply my-4 mx-auto min-w-0;
    @apply rounded-full overflow-hidden;
    @apply ring-1 ring-inset ring-white dark:ring-slate-950;
    @apply bg-white dark:bg-slate-800 drop-shadow;
}

.tabbar ul {
    @apply list-none p-0 m-0 flex flex-row-reverse items-center justify-center;
}

.tabbar ul li button {
    @apply flex flex-row-reverse space-x-reverse space-x-2 items-center justify-center;
    @apply py-2 px-5 transition-colors;
    @apply focus:bg-amber-500 dark:focus:bg-amber-400;
    @apply text-slate-700 dark:text-slate-200 focus:text-slate-950 dark:focus:text-slate-950;
}

.tabbar ul li button svg {
    @apply size-6 opacity-75;
    @apply flex-shrink-0;
}

.tabbar ul li button.active {
    @apply bg-amber-500 dark:bg-amber-400;
    @apply text-slate-950 dark:text-slate-950;
}

.tabbar ul li button.active svg {
    @apply opacity-100;
    @apply stroke-[2];
}

.tabbar ul li button h6 {
    @apply font-bold text-sm;
}