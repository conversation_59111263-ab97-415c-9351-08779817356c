import { Component, EventEmitter, Input, Output } from '@angular/core';

export type SelectedHistoryTab = 'ORDERS' | 'TRANSACTIONS';

@Component({
  selector: 'app-history-tabbar',
  templateUrl: './history-tabbar.component.html',
  styleUrl: './history-tabbar.component.css'
})
export class HistoryTabbarComponent {
  @Input() defaultTab: SelectedHistoryTab = 'ORDERS';
  @Output() onTabChange = new EventEmitter<SelectedHistoryTab>();

  public selectedTab: SelectedHistoryTab;
  constructor() {
    this.selectedTab = this.defaultTab;
  }

  public setSelectedTab(tab: SelectedHistoryTab): void {
    this.onTabChange.emit(tab);
    this.selectedTab = tab;
  }
}
