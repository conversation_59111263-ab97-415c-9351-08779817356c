import { APP_ID, Component, Inject, Injectable, OnD<PERSON>roy, OnInit, PLATFORM_ID } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, Subject, Subscription, takeUntil } from 'rxjs';
import { ThemeService } from '../../services/theme.service';

const THEME_KEY = 'THEME';
export type ThemeValue = 'light' | 'dark';

@UntilDestroy()
@Component({
  selector: 'app-theme-toggle',
  templateUrl: './theme-toggle.component.html',
  styleUrl: './theme-toggle.component.css'
})
export class ThemeToggleComponent {
  constructor(private themeService: ThemeService) { }

  theme$: BehaviorSubject<string> = new BehaviorSubject<string>('dark');


  ngOnInit(): void {
    this.themeService.theme$?.pipe(untilDestroyed(this)).subscribe((value) => {
      this.theme$?.next(value);
    });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }
}