<div class="card-row flex flex-col space-y-4">
    <div class="key-value-row">
        <div class="key-value-item">
            <h6>شماره تیکت</h6>
            <h5 class="number text-slate-800 dark:text-slate-50">#{{ displayTicket.id }}</h5>
        </div>

        <div class="key-value-item">
            <h6>زمان ایجاد</h6>
            <h5 class="number text-slate-700 dark:text-slate-100">{{ displayTicket.createdAt | date:"yyyy/dd/M, h:mm a" }}</h5>
        </div>
    </div>

    <div class="ticket-title">
        <h4 class="text-slate-800 dark:text-slate-100 font-bold text-right">{{ displayTicket.title }}</h4>
    </div>

    <div class="key-value-row">
        <div class="key-value-item">
            <h6>دسته‌بندی</h6>
            <h5 class="text-slate-700 dark:text-slate-100">{{ getCategoryLabel(displayTicket.category) }}</h5>
        </div>

        <div class="key-value-item">
            <h6>اولویت</h6>
            <h5 [class]="getPriorityColor(displayTicket.priority)">{{ getPriorityLabel(displayTicket.priority) }}</h5>
        </div>
    </div>

    <div class="key-value-row mb-2">
        <div class="key-value-item">
            <h6>وضعیت</h6>
            <div class="flex flex-row-reverse space-x-reverse space-x-2 items-center">
                <h5 [class]="getStatusColor(displayTicket.status)">{{ getStatusLabel(displayTicket.status) }}</h5>
                <div class="status-indicator" [class]="getStatusIndicatorColor(displayTicket.status)"></div>
            </div>
        </div>

        <div class="key-value-item">
            <button class="button row small transparent">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" 
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" 
                        d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>
                <span>مشاهده</span>
            </button>
        </div>
    </div>
</div>
