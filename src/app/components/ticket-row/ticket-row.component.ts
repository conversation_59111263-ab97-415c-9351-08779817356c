import { Component, Input } from '@angular/core';
import { SupportTicket, TicketStatus, TicketPriority, TicketCategory } from '../../models/support-ticket.model';

@Component({
  selector: 'app-ticket-row',
  templateUrl: './ticket-row.component.html',
  styleUrl: './ticket-row.component.css'
})
export class TicketRowComponent {
  @Input() ticket?: SupportTicket;

  // Mock data for demonstration
  public mockTicket: SupportTicket = {
    id: '12345',
    title: 'مشکل در پرداخت',
    description: 'پرداخت من انجام شده اما اعتبار به حساب اضافه نشده است',
    category: 'PAYMENT',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: 'user123'
  };

  get displayTicket(): SupportTicket {
    return this.ticket || this.mockTicket;
  }

  getStatusLabel(status: TicketStatus): string {
    const statusMap = {
      'OPEN': 'باز',
      'IN_PROGRESS': 'در حال بررسی',
      'RESOLVED': 'حل شده',
      'CLOSED': 'بسته شده'
    };
    return statusMap[status];
  }

  getStatusColor(status: TicketStatus): string {
    const colorMap = {
      'OPEN': 'text-blue-600 dark:text-blue-400',
      'IN_PROGRESS': 'text-amber-600 dark:text-amber-400',
      'RESOLVED': 'text-green-600 dark:text-green-400',
      'CLOSED': 'text-slate-600 dark:text-slate-400'
    };
    return colorMap[status];
  }

  getPriorityLabel(priority: TicketPriority): string {
    const priorityMap = {
      'LOW': 'کم',
      'MEDIUM': 'متوسط',
      'HIGH': 'بالا',
      'URGENT': 'فوری'
    };
    return priorityMap[priority];
  }

  getPriorityColor(priority: TicketPriority): string {
    const colorMap = {
      'LOW': 'text-slate-600 dark:text-slate-400',
      'MEDIUM': 'text-amber-600 dark:text-amber-400',
      'HIGH': 'text-orange-600 dark:text-orange-400',
      'URGENT': 'text-red-600 dark:text-red-400'
    };
    return colorMap[priority];
  }

  getCategoryLabel(category: TicketCategory): string {
    const categoryMap = {
      'TECHNICAL': 'فنی',
      'PAYMENT': 'پرداخت',
      'ACCOUNT': 'حساب کاربری',
      'GENERAL': 'عمومی'
    };
    return categoryMap[category];
  }

  getStatusIndicatorColor(status: TicketStatus): string {
    const colorMap = {
      'OPEN': 'bg-blue-500',
      'IN_PROGRESS': 'bg-amber-500',
      'RESOLVED': 'bg-green-500',
      'CLOSED': 'bg-slate-500'
    };
    return colorMap[status];
  }
}
