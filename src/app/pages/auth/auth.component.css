.auth {
    @apply flex flex-col items-center justify-center;
    @apply w-full h-full pt-6;
}

.auth .header-image {
    @apply p-3 max-w-44;
}

.auth .title {
    @apply text-xl font-bold text-amber-500 dark:text-amber-400;
}

.auth-container {
    @apply rounded-2xl p-3 mt-6 bg-white/50 dark:bg-slate-800/50 mx-auto max-w-xs w-full space-y-6;
    @apply ring-1 ring-slate-100 dark:ring-slate-950 shadow-lg dark:shadow-slate-900;
}

.auth-container .note {
    @apply text-slate-500 dark:text-slate-300;
    @apply text-sm font-medium text-center;
}

.auth-tabs {
    @apply px-4 mx-auto flex items-center justify-evenly space-x-1 w-full max-w-xs;
}

.auth-tabs button {
    @apply px-3 pt-1 text-amber-600 dark:text-amber-500 w-full relative pb-3;
    @apply font-medium;
    transition: all 0.3s;
}

.auth-tabs button::before {
    content: " ";
    position: absolute;
    width: 0px;
    height: 3px;
    border-radius: 8px;
    background-color: #fff;
    left: 50%;
    bottom: 4px;
    transition: all 0.3s;
}

.auth-tabs button:hover::before {
    width: 32px;
    margin-left: -16px;
}

.auth-tabs button.selected:before {
    width: 58px;
    margin-left: -29px;
    @apply bg-amber-500;
}

.auth-tabs button.selected:hover::before {
    @apply bg-amber-500;
}

.input-text {
    @apply tracking-widest text-center text-xl font-medium;
}