import { Component } from '@angular/core';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { ResponseDTO } from '../../models/response.dto';
import { SignInInput } from '../../models/sign_in.input';
import { AuthenticationApi } from '../../web/authentication.api';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { LoadingService } from '../../services/loading.service';

type AuthMode = 'SIGN_IN' | 'SIGN_UP';

@UntilDestroy()
@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrl: './auth.component.css'
})
export class AuthComponent {
  public mobile: string = '';

  public authMode: AuthMode = 'SIGN_IN';
  public isContactValid: boolean = false;
  public error: string = '';

  constructor(
    private authApi: AuthenticationApi,
    private loadingService: LoadingService,
    private router: Router,
    private location: Location) { }

  ngOnInit(): void {
  }

  public backPress(): void {
    this.location.back();
  }

  public changeAuthMode(): void {
    if (this.authMode == 'SIGN_IN') {
      this.authMode = 'SIGN_UP';
    } else {
      this.authMode = 'SIGN_IN';
    }
  }

  public mobileChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    let value: string = target.value.trim().replaceAll('_', '').replaceAll(' ', '');
    if (value.length === 13) {
      this.mobile = value;
      this.isContactValid = true
    } else {
      this.mobile = '';
      this.isContactValid = false;
    }
  }

  public submitRequest(): void {
    this.loadingService.next(true);

    if (this.authMode == 'SIGN_IN') {
      this.signIn(this.mobile);
    } else {
      this.signUp(this.mobile);
    }
  }

  private signIn(contact: string): void {
    const input: SignInInput = {
      mobile: contact,
      role: 'USER',
      source: 'WEB',
      email: '',
    }

    this.authApi.SignIn(input).pipe(untilDestroyed(this)).subscribe({
      next: (response: ResponseDTO<string>) => {
        if (response.error) {
          this.error = response.error;
        }
        else {
          alert(response.result);
          this.router.navigate(['/verify', input.mobile]);
        }
      },
      error: (error) => {
        console.log(error);
      },
      complete: () => {
        this.loadingService.next(false);
      }
    })

  }

  private signUp(contact: string): void {
  }
}
