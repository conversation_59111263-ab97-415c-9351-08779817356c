.auth {
    @apply flex flex-col items-center justify-center;
    @apply w-full h-full pt-6;
}

.auth .header-image {
    @apply p-3 max-w-44;
}

.auth .title {
    @apply text-lg font-bold w-full text-center text-lime-600 dark:text-lime-500;
}

.auth-container {
    @apply rounded-2xl p-3 mt-6 bg-white/50 dark:bg-slate-800/50 mx-auto max-w-xs w-full space-y-6;
    @apply ring-1 ring-slate-100 dark:ring-slate-950 shadow-lg dark:shadow-slate-900;
}

.auth-container .note {
    @apply text-slate-500 dark:text-slate-300;
    @apply text-sm font-bold text-center;
}

.auth-container .contact-value {
    @apply text-amber-600 dark:text-amber-500 font-sans text-lg font-bold tracking-widest pt-1;
}

.timer {
    @apply w-full flex flex-row-reverse space-x-reverse space-x-1 items-center justify-center;
    @apply text-xs;
}

.code-input span input {
    @apply !border-none !h-12 font-mono;
    @apply ring-2 ring-inset ring-transparent focus:ring-amber-500 dark:focus:ring-amber-500 transition-all !important;
    @apply text-xl font-bold text-slate-600 dark:text-slate-200 !important;
    @apply bg-slate-200 dark:bg-slate-700 focus:bg-slate-50 dark:focus:bg-slate-900 !important;
}

