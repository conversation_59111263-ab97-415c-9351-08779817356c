import { Component, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, repeat, Subject, Subscription, takeUntil, timer } from 'rxjs';
import { environment } from '../../../environments/environment.development';
import { ResponseDTO } from '../../models/response.dto';
import { VerificationOutput } from '../../models/verification.output';
import { LeadingZero } from '../../lib/numbers';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { LoadingService } from '../../services/loading.service';
import { AuthenticationApi } from '../../web/authentication.api';
import { VerificationInput } from '../../models/verification.input';
import { AuthorizationKey } from '../../constants';

@UntilDestroy()
@Component({
  selector: 'app-otp-verify',
  templateUrl: './otp-verify.component.html',
  styleUrl: './otp-verify.component.css',
  encapsulation: ViewEncapsulation.None
})
export class OtpVerifyComponent implements OnInit, OnDestroy {

  readonly timerObservable$: Observable<number>;
  private timerSub: Subscription | undefined;
  public countDown: number = environment.VerificationResendDelay;
  public countDownStr: string = environment.VerificationResendDelay.toString();
  private readonly _timer_stop = new Subject<void>();
  private readonly _timer_start = new Subject<void>();


  public mobile: string | undefined;


  constructor(
    private route: ActivatedRoute,
    private authApi: AuthenticationApi,
    private loadingService: LoadingService,
    private router: Router,
  ) {
    this.timerObservable$ = timer(0, 1000)
      .pipe(
        takeUntil(this._timer_stop),
        untilDestroyed(this),
        repeat({
          delay: () => this._timer_start
        })
      );

    this.route.params.pipe(untilDestroyed(this)).subscribe((params) => {
      this.mobile = params['contact_value'];
      console.log("mobile: ", this.mobile)
    });
  }

  ngOnInit(): void {
    this.timerSub = this.timerObservable$.subscribe(
      (value: number) => {
        const remained: number = environment.VerificationResendDelay - value;
        this.countDown = remained;
        this.countDownStr = LeadingZero(remained, 3);

        if (value >= environment.VerificationResendDelay) this.stopTimer();
      }
    );

    setTimeout(() => {
      this.loadingService.next(false);
    }, 3000);
  }

  private startTimer(): void {
    this._timer_start.next();
  }

  private stopTimer(): void {
    this._timer_stop.next();
  }

  public ResendVerification(): void {
    this.startTimer();
  }

  public onCodeCompleted(code: string): void {
    if (this.mobile) {
      const input: VerificationInput = {
        mobile: this.mobile,
        verification_code: code,
        email: ''
      }

      this.loadingService.next(true);

      // sleep for 3 seconds
      setTimeout(() => {
        this.loadingService.next(false);
        this.router.navigate(['/dashboard']);
      }, 3000);
    }
  }

  ngOnDestroy() {
    this.timerSub?.unsubscribe();
  }

  private handleError(error: string): void {
    let message = '';

    switch (error) {
      case 'provided otp is not correct':
        message = 'رمز یکبار مصرف اشتباه است';
        break;
      case 'previous otp not expired, please wait a few minutes':
        message = 'بتازگی رمز یکبار مثرف برای شما ارسال گردیده، لطفا آنرا وارد نمایید و یا چند دقیقه دیگر مجددا امتحان کنید ';
        break;
      case 'sms api service error':
        message = 'متاسفانه مشکلی در ارسال رمز یکبار مصرف رخ داده، لطفا چند دقیقه دیگر مجددا امتحان کنید.';
        break;
      default:
        message = 'خطا در برقراری ارتباط با سرور، لطفا چند دقیقه دیگر امتحان کنید.';
    }

    // this.alertService.error(message, error, { title: 'خطای رمز یکبار مصرف', autoClose: false });
  }

}

