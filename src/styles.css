@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "yekan-b";
  font-style: normal;
  font-weight: 500;
  src: url(/assets/fonts/iranyekan/eot/iranyekanwebbold.eot);
  src:
    url(/assets/fonts/iranyekan/eot/iranyekanwebbold.eot?#iefix) format("embedded-opentype"),
    url(/assets/fonts/iranyekan/woff/iranyekanwebbold.woff) format("woff"),
    url(/assets/fonts/iranyekan/ttf/iranyekanwebbold.ttf) format("truetype");
}

@font-face {
  font-family: "yekan-r";
  font-style: normal;
  font-weight: 300;
  src: url(/assets/fonts/iranyekan/eot/iranyekanwebregular.eot);
  src:
    url(/assets/fonts/iranyekan/eot/iranyekanwebregular.eot?#iefix) format("embedded-opentype"),
    url(/assets/fonts/iranyekan/woff/iranyekanwebregular.woff) format("woff"),
    url(/assets/fonts/iranyekan/ttf/iranyekanwebregular.ttf) format("truetype");
}

@font-face {
  font-family: "yekan-l";
  font-style: normal;
  font-weight: 200;
  src: url(/assets/fonts/iranyekan/eot/iranyekanweblight.eot);
  src:
    url(/assets/fonts/iranyekan/eot/iranyekanweblight.eot?#iefix) format("embedded-opentype"),
    url(/assets/fonts/iranyekan/woff/iranyekanweblight.woff) format("woff"),
    url(/assets/fonts/iranyekan/ttf/iranyekanweblight.ttf) format("truetype");
}

@layer base {
  :root {
  }
}

@media (prefers-color-scheme: dark) {
  @layer base {
    :root {
    }
  }
}

body {
  font-family: "yekan-r";
  @apply tracking-normal bg-slate-50 dark:bg-slate-900;
  @apply text-slate-500 dark:text-slate-300 text-balance;
}

.font-yekan {
  font-family: "yekan-r" !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  @apply bg-slate-500;
}

/* Track */
::-webkit-scrollbar-track {
  width: 6px;
  @apply bg-slate-500 dark:bg-slate-900;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-amber-400;
  border-radius: 8px;
  -moz-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  @apply bg-amber-500;
  -moz-transition: all 0.5s linear;
  transition: all 0.5s linear;
}

input[type="number"] {
  -moz-appearance: textfield;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

html {
  @apply antialiased;
}

html.no-scroll body {
  @apply overflow-hidden;
}

.rtl {
  direction: rtl;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.button {
  @apply p-3 font-bold rounded-xl transition-all;
  @apply ring-inset;
}

.button.iconic {
  @apply w-full flex flex-row-reverse space-x-reverse space-x-2 items-center justify-center;
}

.button.link {
  @apply bg-slate-50 dark:bg-slate-800 block w-full text-center font-normal;
  @apply hover:bg-slate-100 dark:hover:bg-slate-700;
}

.button.primary {
  @apply bg-amber-500 hover:bg-amber-500 text-white;
}

.button.indigo {
  @apply bg-indigo-500 hover:bg-indigo-600 text-white;
}

.button.green {
  @apply bg-green-600 hover:bg-green-500 text-white;
}

.button.row.small.slate {
  @apply pr-2 pl-4 py-3 text-sm font-bold rounded-xl transition-all;
  @apply hover:text-amber-600 dark:text-white;
  @apply bg-slate-200 hover:bg-slate-100 dark:bg-slate-700 dark:hover:bg-slate-900;
  @apply flex flex-row-reverse space-x-reverse space-x-2 items-center justify-center;
}

.button.row.small.transparent {
  @apply pr-2 pl-4 py-2  text-xs sm:text-sm font-medium rounded-xl transition-all;
  @apply text-amber-400 hover:text-amber-500;
  @apply flex flex-row-reverse space-x-reverse space-x-2 items-center justify-center;
}

.button.row.small svg {
  @apply w-5 h-5 sm:w-6 sm:h-6;
}

.page {
  @apply container mx-auto pt-[58px] pb-[72px];
}

.input-text {
  @apply outline-none rounded-md text-base;
  @apply transition-all;
  @apply outline-none ring-2;
  @apply w-full px-3 py-2;
  @apply bg-slate-50 focus:bg-slate-100 dark:bg-slate-900 dark:focus:bg-slate-950;
  @apply text-slate-600 dark:text-slate-200;
  @apply focus:text-slate-600 dark:focus:text-slate-300;
  @apply ring-slate-200 focus:ring-slate-400;
  @apply dark:ring-slate-700 focus:ring-slate-600;
}

.input-text.number {
  @apply font-sans;
}

.input-text.no-focus {
  @apply focus:bg-slate-50  dark:focus:bg-slate-900;
  @apply focus:text-slate-600 dark:focus:text-slate-200;
}

.input-group {
  @apply flex flex-col items-end space-y-1 pt-3;
}

.input-group .label-text {
  @apply text-sm font-medium pb-1;
}

.card-row {
  @apply py-3 px-4 mx-3 rounded-xl shadow-md;
  @apply shadow-slate-400/15 dark:shadow-black/50;
  @apply bg-white/50 dark:bg-slate-800/50;
  @apply ring-1 ring-slate-200/50 dark:ring-slate-950/50;
}

.card-row.no-padding {
  @apply p-0;
}

.file-input {
  @apply flex-col space-y-2 items-center justify-center;
}

.file-input .photo-select-button {
  @apply outline-none rounded-md;
  @apply font-sans transition-all;
  @apply outline-none ring-2;
  @apply bg-slate-50 focus:bg-slate-100 dark:bg-slate-900 dark:focus:bg-slate-950;
  @apply text-slate-600 dark:text-slate-200;
  @apply focus:text-slate-600 dark:focus:text-slate-300;
  @apply ring-slate-200 focus:ring-slate-400;
  @apply dark:ring-slate-700 focus:ring-slate-600;
  @apply flex items-center justify-center;
}

.text-value {
  @apply outline-none ring-2 rounded-md transition-all text-base;
  @apply bg-slate-50 focus:bg-slate-100 dark:bg-slate-900 dark:focus:bg-slate-950;
  @apply text-slate-600 dark:text-slate-200;
  @apply focus:text-slate-600 dark:focus:text-slate-300;
  @apply ring-slate-200 focus:ring-slate-400;
  @apply dark:ring-slate-700 focus:ring-slate-600;
  @apply w-full px-3 py-2 font-bold;
}

textarea {
  font-family: "yekan-l" !important;
  font-size: 16px;
}

.key-value-row {
  @apply flex flex-row-reverse space-x-reverse space-x-2 items-center justify-between;
  @apply px-1;
}

.key-value-row .key-value-item {
  @apply flex flex-col items-end justify-end;
}

.key-value-row .key-value-item h6 {
  @apply text-sm text-slate-400 dark:text-slate-400;
}

.key-value-row .key-value-item h5 {
  @apply text-right w-full;
}

.key-value-row .key-value-item h5.number {
  @apply font-sans tracking-wider;
}

.key-value-row .key-value-item .united-value {
  @apply flex flex-row-reverse space-x-reverse space-x-2 items-baseline;
}

.key-value-row .key-value-item .united-value small {
  @apply text-sm font-bold;
}
