{"name": "liveutil-grams-pwa", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@ngneat/until-destroy": "^10.0.0", "angular-code-input": "^2.0.0", "apexcharts": "^3.54.0", "ng-apexcharts": "^1.12.0", "ngx-mask": "^18.0.0", "rxjs": "~7.8.0", "swiper": "^11.1.14", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.6", "@angular/cli": "^18.2.6", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "~5.5.2"}}