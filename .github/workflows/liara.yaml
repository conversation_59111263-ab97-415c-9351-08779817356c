name: CD-GRAMS

on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "18"
      - name: update-grams
        env:
          LIARA_TOKEN: ${{ secrets.LIARA_TOKEN }}
        run: |
          npm install -g @liara/cli@3
          liara deploy --app="grams" --api-token="$LIARA_TOKEN" --detach --port=4200 --platform=angular